package controllers

import (
	"errors"
	"qing-users-passport/utils"
	"strconv"
	"time"

	"github.com/kataras/iris/v12"
	"github.com/sirupsen/logrus"
)

// SmsController handles SMS-related endpoints
type SmsController struct{}

// NewSmsController creates a new SMS controller
func NewSmsController() *SmsController {
	return &SmsController{}
}

// SmsOtpRequest represents the request structure for SMS OTP
type SmsOtpRequest struct {
	Phone    string `json:"phone" validate:"required"`
	Scenario string `json:"scenario" validate:"required"`
}

// Valid scenarios for SMS OTP
const (
	ScenarioLogin     = "login"
	ScenarioForgetPwd = "forget_pwd"
)

// Rate limiting constants
const (
	MaxSmsPerHour = 5                // Maximum SMS per phone number per hour
	OtpExpiration = 10 * time.Minute // OTP expiration time
)

// SendOtp handles POST /api/login/sms-otp
// Sends SMS OTP verification code to the specified phone number
func (s *SmsController) SendOtp(ctx iris.Context) {
	var req SmsOtpRequest

	// Parse JSON request body
	if err := ctx.ReadJSON(&req); err != nil {
		logrus.WithError(err).Error("Failed to parse SMS OTP request")
		utils.ValidationError(ctx, "Invalid request format")
		return
	}

	// Validate required fields
	if req.Phone == "" {
		utils.ValidationError(ctx, "Phone number is required")
		return
	}

	if req.Scenario == "" {
		utils.ValidationError(ctx, "Scenario is required")
		return
	}

	// Validate scenario
	if req.Scenario != ScenarioLogin && req.Scenario != ScenarioForgetPwd {
		utils.ValidationError(ctx, "Invalid scenario. Allowed values: login, forget_pwd")
		return
	}

	// Validate phone number format
	if !utils.ValidatePhoneNumber(req.Phone) {
		utils.ValidationError(ctx, "Invalid phone number format")
		return
	}

	// Get device ID from header (optional for public endpoint)
	deviceID := ctx.GetHeader("X-Device-Id")
	if deviceID == "" {
		deviceID = "unknown"
	}

	// Check rate limiting
	if err := s.checkRateLimit(req.Phone); err != nil {
		logrus.WithFields(logrus.Fields{
			"phone":     req.Phone,
			"device_id": deviceID,
			"scenario":  req.Scenario,
		}).Warn("SMS rate limit exceeded")

		utils.Error(ctx, utils.CodeValidationError, "Too many SMS requests. Please try again later.")
		return
	}

	// Generate OTP
	otp := utils.GenerateOTP()

	// Store OTP in Redis
	otpKey := utils.BuildOTPRedisKey(req.Scenario, req.Phone, deviceID)
	if err := utils.SetCache(otpKey, otp, OtpExpiration); err != nil {
		logrus.WithError(err).Error("Failed to store OTP in Redis")
		utils.InternalError(ctx, "Failed to process request")
		return
	}

	// Update rate limiting counter
	s.updateRateLimit(req.Phone)

	// Send SMS (don't expose SMS sending errors to client for security)
	if err := utils.SendSmsOtp(req.Phone, otp); err != nil {
		logrus.WithFields(logrus.Fields{
			"phone":     req.Phone,
			"device_id": deviceID,
			"scenario":  req.Scenario,
			"error":     err.Error(),
		}).Error("Failed to send SMS OTP")

		// Still return success to client for security reasons
		// Don't expose SMS service failures
	}

	// Log successful OTP generation
	logrus.WithFields(logrus.Fields{
		"phone":     req.Phone,
		"device_id": deviceID,
		"scenario":  req.Scenario,
	}).Info("SMS OTP generated and sent")

	// Return success response (always success for security)
	utils.SuccessWithMsg(ctx, nil, "Verification code sent successfully")
}

// checkRateLimit checks if the phone number has exceeded SMS rate limit
func (s *SmsController) checkRateLimit(phone string) error {
	rateLimitKey := utils.BuildRateLimitRedisKey(phone)

	// Get current count
	countStr, err := utils.GetCache(rateLimitKey)
	if err != nil {
		// If key doesn't exist, it's the first request
		return nil
	}

	count, err := strconv.Atoi(countStr)
	if err != nil {
		// If conversion fails, reset counter
		return nil
	}

	if count >= MaxSmsPerHour {
		return errors.New("rate limit exceeded")
	}

	return nil
}

// updateRateLimit updates the rate limiting counter for the phone number
func (s *SmsController) updateRateLimit(phone string) {
	rateLimitKey := utils.BuildRateLimitRedisKey(phone)

	// Get current count
	countStr, err := utils.GetCache(rateLimitKey)
	if err != nil {
		// First request, set to 1
		utils.SetCache(rateLimitKey, "1", time.Hour)
		return
	}

	count, err := strconv.Atoi(countStr)
	if err != nil {
		// If conversion fails, reset to 1
		utils.SetCache(rateLimitKey, "1", time.Hour)
		return
	}

	// Increment counter
	newCount := count + 1
	utils.SetCache(rateLimitKey, strconv.Itoa(newCount), time.Hour)
}
