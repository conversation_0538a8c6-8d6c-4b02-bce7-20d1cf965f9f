package controllers

import (
	"qing-users-passport/utils"

	"github.com/kataras/iris/v12"
)

// ConfigController handles configuration-related endpoints
type ConfigController struct{}

// NewConfigController creates a new config controller
func NewConfigController() *ConfigController {
	return &ConfigController{}
}

// BackgroundConfig represents background configuration
type BackgroundConfig struct {
	Type string `json:"type"` // "image" or "video" (future support)
	URL  string `json:"url"`
}

// LoginConfig represents login page configuration
type LoginConfig struct {
	Background BackgroundConfig `json:"background"`
}

// GetLoginConfig handles GET /api/login/config
// Returns configuration data for the login page including background settings
func (c *ConfigController) GetLoginConfig(ctx iris.Context) {
	// Create the configuration response
	config := LoginConfig{
		Background: BackgroundConfig{
			Type: "image",
			URL:  "https://fancy.s3.bitiful.net/bg.jpg",
		},
	}

	// Return success response with configuration data
	utils.SuccessWithMsg(ctx, config, "Login configuration retrieved successfully")
}
