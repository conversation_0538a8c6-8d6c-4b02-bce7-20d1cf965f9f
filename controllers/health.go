package controllers

import (
	"qing-users-passport/utils"

	"github.com/kataras/iris/v12"
)

// HealthController handles health check endpoints
type HealthController struct{}

// NewHealthController creates a new health controller
func NewHealthController() *HealthController {
	return &HealthController{}
}

// Check handles GET /health
func (h *HealthController) Check(ctx iris.Context) {
	// Perform database health checks
	healthResults := utils.HealthCheckDatabases()

	// Check if any database is unhealthy
	allHealthy := true
	for _, err := range healthResults {
		if err != nil {
			allHealthy = false
			break
		}
	}

	if allHealthy {
		utils.SuccessWithMsg(ctx, map[string]string{
			"status":     "healthy",
			"postgresql": "connected",
			"redis":      "connected",
		}, "Service is healthy")
	} else {
		// Convert errors to strings for JSON response
		errorMap := make(map[string]interface{})
		for service, err := range healthResults {
			if err != nil {
				errorMap[service] = err.Error()
			} else {
				errorMap[service] = "connected"
			}
		}

		utils.ErrorWithData(ctx, utils.CodeInternalError, "Service is unhealthy", errorMap)
	}
}

// Ping handles GET /ping - simple ping endpoint
func (h *HealthController) Ping(ctx iris.Context) {
	utils.SuccessWithMsg(ctx, map[string]string{
		"status": "pong",
	}, "Service is running")
}
