package middleware

import (
	"qing-users-passport/utils"
	"strings"

	"github.com/kataras/iris/v12"
	"github.com/sirupsen/logrus"
)

// RequiredHeaders middleware validates that all required headers are present
func RequiredHeaders() iris.Handler {
	return func(ctx iris.Context) {
		// Required headers
		requiredHeaders := []string{
			"Authorization",
			"X-Signature",
			"X-Device-Id",
		}

		// Check each required header
		for _, header := range requiredHeaders {
			value := ctx.GetHeader(header)
			if value == "" {
				logrus.WithFields(logrus.Fields{
					"missing_header": header,
					"path":           ctx.Path(),
					"method":         ctx.Method(),
					"remote_addr":    ctx.RemoteAddr(),
				}).Warn("Missing required header")

				utils.Error(ctx, utils.CodeMissingHeaders, "Missing required header: "+header)
				return
			}
		}

		// Validate Authorization header format (should start with "Bearer ")
		authHeader := ctx.GetHeader("Authorization")
		if !strings.HasPrefix(authHeader, "Bearer ") {
			logrus.WithFields(logrus.Fields{
				"auth_header": authHeader,
				"path":        ctx.Path(),
				"method":      ctx.Method(),
				"remote_addr": ctx.RemoteAddr(),
			}).Warn("Invalid Authorization header format")

			utils.Error(ctx, utils.CodeUnauthorized, "Invalid Authorization header format")
			return
		}

		// Extract token from Authorization header
		token := strings.TrimPrefix(authHeader, "Bearer ")
		if token == "" {
			logrus.WithFields(logrus.Fields{
				"path":        ctx.Path(),
				"method":      ctx.Method(),
				"remote_addr": ctx.RemoteAddr(),
			}).Warn("Empty token in Authorization header")

			utils.Error(ctx, utils.CodeUnauthorized, "Empty token in Authorization header")
			return
		}

		// Store headers in context for later use
		ctx.Values().Set("auth_token", token)
		ctx.Values().Set("signature", ctx.GetHeader("X-Signature"))
		ctx.Values().Set("device_id", ctx.GetHeader("X-Device-Id"))

		logrus.WithFields(logrus.Fields{
			"path":      ctx.Path(),
			"method":    ctx.Method(),
			"device_id": ctx.GetHeader("X-Device-Id"),
		}).Debug("Headers validation passed")

		ctx.Next()
	}
}

// CORS middleware handles Cross-Origin Resource Sharing
func CORS() iris.Handler {
	return func(ctx iris.Context) {
		ctx.Header("Access-Control-Allow-Origin", "*")
		ctx.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		ctx.Header("Access-Control-Allow-Headers", "Content-Type, Authorization, X-Signature, X-Device-Id")
		ctx.Header("Access-Control-Max-Age", "86400")

		if ctx.Method() == iris.MethodOptions {
			ctx.StatusCode(iris.StatusNoContent)
			return
		}

		ctx.Next()
	}
}

// RequestLogger middleware logs incoming requests
func RequestLogger() iris.Handler {
	return func(ctx iris.Context) {
		logrus.WithFields(logrus.Fields{
			"method":      ctx.Method(),
			"path":        ctx.Path(),
			"remote_addr": ctx.RemoteAddr(),
			"user_agent":  ctx.GetHeader("User-Agent"),
			"device_id":   ctx.GetHeader("X-Device-Id"),
		}).Info("Incoming request")

		ctx.Next()

		logrus.WithFields(logrus.Fields{
			"method":      ctx.Method(),
			"path":        ctx.Path(),
			"status_code": ctx.GetStatusCode(),
			"remote_addr": ctx.RemoteAddr(),
		}).Info("Request completed")
	}
}
