package main

import (
	"context"
	"os"
	"os/signal"
	"qing-users-passport/config"
	"qing-users-passport/controllers"
	"qing-users-passport/middleware"
	"qing-users-passport/utils"
	"syscall"
	"time"

	"github.com/kataras/iris/v12"
	"github.com/kataras/iris/v12/middleware/logger"
	"github.com/kataras/iris/v12/middleware/recover"
	"github.com/sirupsen/logrus"
)

func main() {
	// Load configuration
	cfg := config.Load()

	// Configure logging
	setupLogging(cfg)

	// Initialize databases
	if err := utils.InitDatabases(cfg); err != nil {
		logrus.WithError(err).Fatal("Failed to initialize databases")
	}
	defer utils.CloseDatabases()

	// Create Iris app
	app := iris.New()

	// Configure Iris
	setupIris(app)

	// Setup middleware
	setupMiddleware(app)

	// Setup routes
	setupRoutes(app)

	// Start server with graceful shutdown
	startServer(app, cfg)
}

func setupLogging(cfg *config.Config) {
	// Set log level based on environment
	if cfg.Server.Env == "production" {
		logrus.SetLevel(logrus.InfoLevel)
		logrus.SetFormatter(&logrus.JSONFormatter{})
	} else {
		logrus.SetLevel(logrus.DebugLevel)
		logrus.SetFormatter(&logrus.TextFormatter{
			FullTimestamp: true,
		})
	}

	logrus.WithField("env", cfg.Server.Env).Info("Logging configured")
}

func setupIris(app *iris.Application) {
	// Configure Iris settings
	app.Configure(iris.WithConfiguration(iris.Configuration{
		DisableStartupLog:                 false,
		DisableInterruptHandler:           true,
		DisablePathCorrection:             false,
		EnablePathEscape:                  false,
		FireMethodNotAllowed:              true,
		DisableBodyConsumptionOnUnmarshal: false,
		TimeFormat:                        "2006-01-02 15:04:05",
		Charset:                           "UTF-8",
	}))
}

func setupMiddleware(app *iris.Application) {
	// Recovery middleware
	app.Use(recover.New())

	// Request logger middleware
	app.Use(logger.New())

	// CORS middleware
	app.Use(middleware.CORS())

	// Request logging middleware
	app.Use(middleware.RequestLogger())
}

func setupRoutes(app *iris.Application) {
	// Health controller
	healthController := controllers.NewHealthController()

	// Public routes (no header validation required)
	public := app.Party("/")
	{
		public.Get("/ping", healthController.Ping)
		public.Get("/health", healthController.Check)
	}

	// API routes (require header validation)
	api := app.Party("/api/v1")
	api.Use(middleware.RequiredHeaders())
	{
		// Authentication endpoints will be added here later
		api.Get("/test", func(ctx iris.Context) {
			utils.SuccessWithMsg(ctx, map[string]string{
				"message": "API is working",
				"token":   ctx.Values().GetString("auth_token"),
			}, "Test endpoint")
		})
	}
}

func startServer(app *iris.Application, cfg *config.Config) {
	// Server address
	addr := cfg.Server.Host + ":" + cfg.Server.Port

	// Create a channel to listen for interrupt signals
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)

	// Start server in a goroutine
	go func() {
		logrus.WithFields(logrus.Fields{
			"host": cfg.Server.Host,
			"port": cfg.Server.Port,
			"env":  cfg.Server.Env,
		}).Info("Starting HTTP server")

		if err := app.Listen(addr); err != nil {
			logrus.WithError(err).Error("Server failed to start")
		}
	}()

	// Wait for interrupt signal
	<-quit
	logrus.Info("Shutting down server...")

	// Create a context with timeout for graceful shutdown
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// Shutdown the server
	if err := app.Shutdown(ctx); err != nil {
		logrus.WithError(err).Error("Server forced to shutdown")
	} else {
		logrus.Info("Server gracefully stopped")
	}
}
