# Qing Users Passport

A user authentication HTTP server built with Go, Iris v12, Redis, and PostgreSQL.

## Features

- **Iris v12 Framework**: High-performance web framework
- **Redis Integration**: Session management and caching using rueidis client
- **PostgreSQL Support**: User data persistence with connection pooling
- **Standardized API**: Consistent JSON response format with code/data/msg/extra fields
- **Header Validation**: Required headers middleware (Authorization, X-Signature, X-Device-Id)
- **Health Checks**: Database connectivity monitoring
- **Graceful Shutdown**: Proper server lifecycle management
- **Environment Configuration**: Flexible configuration via environment variables

## Project Structure

```
qing-users-passport/
├── config/          # Configuration management
├── controllers/     # HTTP request handlers
├── middleware/      # Custom middleware (headers, CORS, logging)
├── models/          # Data models and structures
├── services/        # Business logic layer
├── utils/           # Utility functions (database, response helpers)
├── .env.example     # Environment variables template
├── go.mod           # Go module dependencies
└── main.go          # Application entry point
```

## Dependencies

- **Web Framework**: `github.com/kataras/iris/v12`
- **Redis Client**: `github.com/redis/rueidis`
- **PostgreSQL Driver**: `github.com/lib/pq`
- **Configuration**: `github.com/joho/godotenv`
- **Logging**: `github.com/sirupsen/logrus`
## API Standards

### Required Headers
All API endpoints (except health checks) require these headers:
- `Authorization`: Bearer token format
- `X-Signature`: Request signature for security
- `X-Device-Id`: Unique device identifier

### Request Format
- **GET**: Query parameters
- **POST**: JSON body

### Response Format
All responses follow this JSON structure:
```json
{
  "code": 0,
  "data": {},
  "msg": "Success",
  "extra": {}
}
```

- `code`: 0 for success, non-zero for errors
- `data`: Response payload
- `msg`: Human-readable message
- `extra`: Additional metadata (optional)

## Setup

1. **Clone and install dependencies**:
```bash
go mod tidy
```

2. **Configure environment**:
```bash
cp .env.example .env
# Edit .env with your database credentials
```

3. **Build the application**:
```bash
go build -o bin/qing-users-passport .
```

4. **Run the server**:
```bash
./bin/qing-users-passport
```

## Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `SERVER_HOST` | Server host | `localhost` |
| `SERVER_PORT` | Server port | `8080` |
| `ENV` | Environment (development/production) | `development` |
| `DB_HOST` | PostgreSQL host | `localhost` |
| `DB_PORT` | PostgreSQL port | `5432` |
| `DB_USER` | PostgreSQL username | `postgres` |
| `DB_PASSWORD` | PostgreSQL password | `` |
| `DB_NAME` | PostgreSQL database name | `qing_users_passport` |
| `DB_SSLMODE` | PostgreSQL SSL mode | `disable` |
| `REDIS_HOST` | Redis host | `localhost` |
| `REDIS_PORT` | Redis port | `6379` |
| `REDIS_PASSWORD` | Redis password | `` |
| `REDIS_DB` | Redis database number | `0` |

## Available Endpoints

### Health Checks
- `GET /ping` - Simple ping endpoint (no headers required)
- `GET /health` - Database connectivity check (no headers required)

### API Endpoints
- `GET /api/v1/test` - Test endpoint with header validation

## Next Steps

The foundational structure is now ready. You can implement:

1. **Authentication endpoints** (login, register, logout)
2. **User management** (profile, password reset)
3. **Session management** (token refresh, validation)
4. **Database migrations** (user tables, indexes)
5. **Input validation** (request validation middleware)
6. **Rate limiting** (API rate limiting middleware)
7. **Unit tests** (comprehensive test coverage)

## Development

To run in development mode:
```bash
ENV=development go run main.go
```

The server will start on `http://localhost:8080` by default.