package utils

import (
	"fmt"
	"qing-users-passport/config"

	"github.com/sirupsen/logrus"
)

// InitDatabases initializes both PostgreSQL and Redis connections
func InitDatabases(cfg *config.Config) error {
	// Initialize PostgreSQL
	if err := InitPostgreSQL(cfg); err != nil {
		return fmt.Errorf("failed to initialize PostgreSQL: %w", err)
	}

	// Initialize Redis
	if err := InitRedis(cfg); err != nil {
		return fmt.Errorf("failed to initialize Redis: %w", err)
	}

	logrus.Info("All database connections initialized successfully")
	return nil
}

// CloseDatabases closes all database connections
func CloseDatabases() {
	// Close PostgreSQL
	if err := ClosePostgreSQL(); err != nil {
		logrus.WithError(err).Error("Failed to close PostgreSQL connection")
	}

	// Close Redis
	CloseRedis()

	logrus.Info("All database connections closed")
}

// HealthCheckDatabases checks the health of all database connections
func HealthCheckDatabases() map[string]error {
	results := make(map[string]error)

	// Check PostgreSQL
	if err := HealthCheckPostgreSQL(); err != nil {
		results["postgresql"] = err
		logrus.WithError(err).Error("PostgreSQL health check failed")
	} else {
		results["postgresql"] = nil
		logrus.Debug("PostgreSQL health check passed")
	}

	// Check Redis
	if err := HealthCheckRedis(); err != nil {
		results["redis"] = err
		logrus.WithError(err).Error("Redis health check failed")
	} else {
		results["redis"] = nil
		logrus.Debug("Redis health check passed")
	}

	return results
}
