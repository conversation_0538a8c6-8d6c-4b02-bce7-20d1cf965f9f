package utils

import (
	"fmt"
	"log"
	"math/rand"
	"time"

	openapi "github.com/alibabacloud-go/darabonba-openapi/client"
	dysmsapi "github.com/alibabacloud-go/dysmsapi-20170525/v2/client"
	util "github.com/alibabacloud-go/tea-utils/service"
	"github.com/alibabacloud-go/tea/tea"
)

const ACCESS_KEY_ID = "LTAI5tS5Mn8Yzt89P1szKyGe"
const ACCESS_KEY_SECRET = "******************************"

var smsClient *dysmsapi.Client

const signName = "青初于蓝"
const templateCode = "SMS_323345406"

func init() {
	client, _err := createSmsClient(tea.String(ACCESS_KEY_ID), tea.String(ACCESS_KEY_SECRET))
	if _err != nil {
		log.Printf("create aliyun client error: %v", _err)
		return
	}
	smsClient = client
}

func createSmsClient(accessKeyId *string, accessKeySecret *string) (_result *dysmsapi.Client, _err error) {
	config := &openapi.Config{}
	config.AccessKeyId = accessKeyId
	config.AccessKeySecret = accessKeySecret
	_result = &dysmsapi.Client{}
	_result, _err = dysmsapi.NewClient(config)
	return _result, _err
}

func SendSmsOtp(phone, otp string) error {
	sendReq := &dysmsapi.SendSmsRequest{
		PhoneNumbers:  tea.String(phone),
		SignName:      tea.String(signName),
		TemplateCode:  tea.String(templateCode),
		TemplateParam: tea.String("{\"code\":\"" + otp + "\"}"),
	}
	sendResp, _err := smsClient.SendSms(sendReq)
	if _err != nil {
		return _err
	}

	code := sendResp.Body.Code
	if !tea.BoolValue(util.EqualString(code, tea.String("OK"))) {
		log.Printf("SendSmsOtp 错误信息: %v", tea.StringValue(sendResp.Body.Message))
		return _err
	}

	return nil
}

// GenerateOTP generates a random 6-digit numeric OTP
func GenerateOTP() string {
	// Use current time as seed for better randomness
	rand.Seed(time.Now().UnixNano())

	// Generate 6-digit number (100000 to 999999)
	otp := rand.Intn(900000) + 100000

	return fmt.Sprintf("%06d", otp)
}

// ValidatePhoneNumber performs basic phone number validation
func ValidatePhoneNumber(phone string) bool {
	// Basic validation: should be 11 digits starting with 1
	if len(phone) != 11 {
		return false
	}

	// Check if it starts with 1 (Chinese mobile number format)
	if phone[0] != '1' {
		return false
	}

	// Check if all characters are digits
	for _, char := range phone {
		if char < '0' || char > '9' {
			return false
		}
	}

	return true
}

// BuildOTPRedisKey builds the Redis key for storing OTP
func BuildOTPRedisKey(scenario, phone, deviceID string) string {
	return fmt.Sprintf("otp:%s:%s:%s", scenario, phone, deviceID)
}

// BuildRateLimitRedisKey builds the Redis key for rate limiting
func BuildRateLimitRedisKey(phone string) string {
	return fmt.Sprintf("sms_rate_limit:%s", phone)
}
