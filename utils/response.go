package utils

import (
	"github.com/kataras/iris/v12"
)

// StandardResponse represents the standard API response format
type StandardResponse struct {
	Code  int         `json:"code"`
	Data  interface{} `json:"data"`
	Msg   string      `json:"msg"`
	Extra interface{} `json:"extra"`
}

// ResponseCode constants for different response types
const (
	CodeSuccess          = 0
	CodeInvalidRequest   = 1001
	CodeUnauthorized     = 1002
	CodeForbidden        = 1003
	CodeNotFound         = 1004
	CodeInternalError    = 1005
	CodeValidationError  = 1006
	CodeDatabaseError    = 1007
	CodeRedisError       = 1008
	CodeMissingHeaders   = 1009
	CodeInvalidSignature = 1010
)

// ensureNotNil ensures that data and extra fields are never nil
// Returns empty map if the input is nil, otherwise returns the original value
func ensureNotNil(value interface{}) interface{} {
	if value == nil {
		return map[string]interface{}{}
	}
	return value
}

// Success sends a successful response
func Success(ctx iris.Context, data interface{}) {
	response := StandardResponse{
		Code:  CodeSuccess,
		Data:  ensureNotNil(data),
		Msg:   "Success",
		Extra: ensureNotNil(nil),
	}
	ctx.JSON(response)
}

// SuccessWithMsg sends a successful response with custom message
func SuccessWithMsg(ctx iris.Context, data interface{}, msg string) {
	response := StandardResponse{
		Code:  CodeSuccess,
		Data:  ensureNotNil(data),
		Msg:   msg,
		Extra: ensureNotNil(nil),
	}
	ctx.JSON(response)
}

// SuccessWithExtra sends a successful response with extra data
func SuccessWithExtra(ctx iris.Context, data interface{}, msg string, extra interface{}) {
	response := StandardResponse{
		Code:  CodeSuccess,
		Data:  ensureNotNil(data),
		Msg:   msg,
		Extra: ensureNotNil(extra),
	}
	ctx.JSON(response)
}

// Error sends an error response
func Error(ctx iris.Context, code int, msg string) {
	response := StandardResponse{
		Code:  code,
		Data:  ensureNotNil(nil),
		Msg:   msg,
		Extra: ensureNotNil(nil),
	}
	ctx.StatusCode(getHTTPStatusCode(code))
	ctx.JSON(response)
}

// ErrorWithData sends an error response with data
func ErrorWithData(ctx iris.Context, code int, msg string, data interface{}) {
	response := StandardResponse{
		Code:  code,
		Data:  ensureNotNil(data),
		Msg:   msg,
		Extra: ensureNotNil(nil),
	}
	ctx.StatusCode(getHTTPStatusCode(code))
	ctx.JSON(response)
}

// ErrorWithExtra sends an error response with extra data
func ErrorWithExtra(ctx iris.Context, code int, msg string, data interface{}, extra interface{}) {
	response := StandardResponse{
		Code:  code,
		Data:  ensureNotNil(data),
		Msg:   msg,
		Extra: ensureNotNil(extra),
	}
	ctx.StatusCode(getHTTPStatusCode(code))
	ctx.JSON(response)
}

// getHTTPStatusCode maps internal error codes to HTTP status codes
func getHTTPStatusCode(code int) int {
	switch code {
	case CodeSuccess:
		return iris.StatusOK
	case CodeInvalidRequest, CodeValidationError:
		return iris.StatusBadRequest
	case CodeUnauthorized, CodeMissingHeaders, CodeInvalidSignature:
		return iris.StatusForbidden
	case CodeForbidden:
		return iris.StatusForbidden
	case CodeNotFound:
		return iris.StatusNotFound
	case CodeInternalError, CodeDatabaseError, CodeRedisError:
		return iris.StatusInternalServerError
	default:
		return iris.StatusInternalServerError
	}
}

// ValidationError sends a validation error response
func ValidationError(ctx iris.Context, msg string) {
	Error(ctx, CodeValidationError, msg)
}

// Unauthorized sends an unauthorized error response
func Unauthorized(ctx iris.Context, msg string) {
	Error(ctx, CodeUnauthorized, msg)
}

// InternalError sends an internal server error response
func InternalError(ctx iris.Context, msg string) {
	Error(ctx, CodeInternalError, msg)
}

// NotFound sends a not found error response
func NotFound(ctx iris.Context, msg string) {
	Error(ctx, CodeNotFound, msg)
}
