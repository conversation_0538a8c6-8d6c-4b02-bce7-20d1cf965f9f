package utils

import (
	"context"
	"fmt"
	"qing-users-passport/config"
	"time"

	"github.com/redis/rueidis"
	"github.com/sirupsen/logrus"
)

var RedisClient rueidis.Client

// InitRedis initializes Redis connection
func InitRedis(cfg *config.Config) error {
	// Build Redis connection options
	options := rueidis.ClientOption{
		InitAddress: []string{fmt.Sprintf("%s:%s", cfg.Redis.Host, cfg.Redis.Port)},
		Password:    cfg.Redis.Password,
		SelectDB:    cfg.Redis.DB,
	}

	// Create Redis client
	client, err := rueidis.NewClient(options)
	if err != nil {
		return fmt.Errorf("failed to create Redis client: %w", err)
	}

	// Test the connection
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := client.Do(ctx, client.B().Ping().Build()).Error(); err != nil {
		return fmt.Errorf("failed to ping Redis: %w", err)
	}

	RedisClient = client

	logrus.WithFields(logrus.Fields{
		"host": cfg.Redis.Host,
		"port": cfg.Redis.Port,
		"db":   cfg.Redis.DB,
	}).Info("Redis connection established")

	return nil
}

// CloseRedis closes the Redis connection
func CloseRedis() {
	if RedisClient != nil {
		logrus.Info("Closing Redis connection")
		RedisClient.Close()
	}
}

// HealthCheckRedis checks if Redis connection is healthy
func HealthCheckRedis() error {
	if RedisClient == nil {
		return fmt.Errorf("Redis client is nil")
	}

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := RedisClient.Do(ctx, RedisClient.B().Ping().Build()).Error(); err != nil {
		return fmt.Errorf("Redis ping failed: %w", err)
	}

	return nil
}

// GetRedisClient returns the Redis client
func GetRedisClient() rueidis.Client {
	return RedisClient
}

// SetCache sets a value in Redis with expiration
func SetCache(key string, value string, expiration time.Duration) error {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	cmd := RedisClient.B().Set().Key(key).Value(value)
	if expiration > 0 {
		cmd = cmd.Ex(expiration)
	}

	return RedisClient.Do(ctx, cmd.Build()).Error()
}

// GetCache gets a value from Redis
func GetCache(key string) (string, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	result := RedisClient.Do(ctx, RedisClient.B().Get().Key(key).Build())
	if result.Error() != nil {
		return "", result.Error()
	}

	return result.ToString()
}

// DeleteCache deletes a key from Redis
func DeleteCache(key string) error {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	return RedisClient.Do(ctx, RedisClient.B().Del().Key(key).Build()).Error()
}

// ExistsCache checks if a key exists in Redis
func ExistsCache(key string) (bool, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	result := RedisClient.Do(ctx, RedisClient.B().Exists().Key(key).Build())
	if result.Error() != nil {
		return false, result.Error()
	}

	count, err := result.ToInt64()
	return count > 0, err
}
